/**
 * TodoTool 内置工具测试用例
 * 验证迁移后的功能正确性和性能提升
 */

const TodoTool = require('../modules/todo-tool')

/**
 * 模拟测试环境
 */
class TestEnvironment {
  constructor() {
    this.todoTool = new TodoTool()
    this.testResults = []
    this.performanceResults = []
  }

  /**
   * 记录测试结果
   * @param {string} testName - 测试名称
   * @param {boolean} passed - 是否通过
   * @param {string} message - 测试消息
   * @param {number} duration - 执行时间
   */
  recordResult(testName, passed, message, duration = 0) {
    this.testResults.push({
      testName,
      passed,
      message,
      duration,
      timestamp: new Date().toISOString()
    })
    
    console.log(`[TEST] ${testName}: ${passed ? 'PASS' : 'FAIL'} - ${message} (${duration}ms)`)
  }

  /**
   * 记录性能测试结果
   * @param {string} operation - 操作名称
   * @param {number} duration - 执行时间
   */
  recordPerformance(operation, duration) {
    this.performanceResults.push({
      operation,
      duration,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    const totalTests = this.testResults.length
    const passedTests = this.testResults.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    
    const avgDuration = this.testResults.length > 0 ? 
      this.testResults.reduce((sum, r) => sum + r.duration, 0) / this.testResults.length : 0

    const performanceStats = this.todoTool.getPerformanceStats()

    return {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        successRate: totalTests > 0 ? (passedTests / totalTests * 100).toFixed(2) + '%' : '0%',
        avgTestDuration: Math.round(avgDuration) + 'ms'
      },
      performanceStats,
      testResults: this.testResults,
      performanceResults: this.performanceResults
    }
  }
}

/**
 * 基础功能测试
 */
async function testBasicFunctionality(env) {
  console.log('\n=== 基础功能测试 ===')
  
  // 测试工具初始化
  try {
    const startTime = Date.now()
    const tool = new TodoTool()
    const duration = Date.now() - startTime
    
    env.recordResult('工具初始化', true, '工具实例创建成功', duration)
  } catch (error) {
    env.recordResult('工具初始化', false, `初始化失败：${error.message}`)
  }

  // 测试方法存在性
  const requiredMethods = [
    'execute', 'getBatchData', 'getTasks', 'createTask', 'updateTask', 
    'deleteTask', 'completeTask', 'uncompleteTask', 'getTask', 'batchOperateTasks',
    'getProjects', 'createProject', 'updateProject', 'deleteProject', 
    'closeProject', 'reopenProject', 'getProject'
  ]

  for (const method of requiredMethods) {
    const exists = typeof env.todoTool[method] === 'function'
    env.recordResult(`方法存在性检查：${method}`, exists, 
      exists ? '方法存在' : '方法不存在')
  }
}



/**
 * 性能统计测试
 */
async function testPerformanceStats(env) {
  console.log('\n=== 性能统计测试 ===')
  
  try {
    // 获取初始统计信息
    const initialStats = env.todoTool.getPerformanceStats()
    
    env.recordResult('性能统计获取', true,
      `请求数：${initialStats.requestCount}, 平均响应时间：${initialStats.avgResponseTime}ms`)

    // 执行一些操作来更新统计信息
    await env.todoTool.getBatchData()
    await env.todoTool.getBatchData()

    const updatedStats = env.todoTool.getPerformanceStats()
    
    env.recordResult('统计信息更新', 
      updatedStats.requestCount > initialStats.requestCount,
      `请求数增加：${initialStats.requestCount} -> ${updatedStats.requestCount}`)

  } catch (error) {
    env.recordResult('性能统计测试', false, `测试失败：${error.message}`)
  }
}

/**
 * createTask 功能测试
 */
async function testCreateTask(env) {
  console.log('\n=== createTask 功能测试 ===')

  // 测试正常创建任务
  try {
    const startTime = Date.now()
    const taskData = {
      title: '测试任务标题',
      content: '这是一个测试任务的详细内容',
      priority: 3,
      projectName: null,
      tagNames: [],
      startDate: null,
      dueDate: null,
      isAllDay: false,
      reminder: null
    }

    const result = await env.todoTool.createTask(taskData)
    const duration = Date.now() - startTime

    // 验证返回结果格式
    const hasCorrectFormat = result &&
      typeof result === 'object' &&
      (result.errCode === null || result.errCode === undefined)

    env.recordResult('正常创建任务', hasCorrectFormat,
      hasCorrectFormat ? '任务创建成功，返回格式正确' : `创建失败：${result?.errMsg || '未知错误'}`,
      duration)

    // 验证返回数据结构
    if (hasCorrectFormat && result.data) {
      const hasRequiredFields = result.data.id || result.data._id
      env.recordResult('返回数据结构验证', hasRequiredFields,
        hasRequiredFields ? '返回数据包含必要字段' : '返回数据缺少必要字段')
    }

  } catch (error) {
    env.recordResult('正常创建任务', false, `创建任务异常：${error.message}`)
  }

  // 测试缺少必要参数的情况
  try {
    const result = await env.todoTool.createTask({})
    const hasError = result && result.errCode

    env.recordResult('缺少必要参数测试', hasError,
      hasError ? `正确返回错误：${result.errMsg}` : '未正确处理缺少参数的情况')

  } catch (error) {
    env.recordResult('缺少必要参数测试', true, '正确抛出异常')
  }

  // 测试空标题参数
  try {
    const result = await env.todoTool.createTask({ title: '' })
    const hasError = result && result.errCode

    env.recordResult('空标题参数测试', hasError,
      hasError ? `正确返回错误：${result.errMsg}` : '未正确处理空标题')

  } catch (error) {
    env.recordResult('空标题参数测试', true, '正确抛出异常')
  }

  // 测试只有空格的标题
  try {
    const result = await env.todoTool.createTask({ title: '   ' })
    const hasError = result && result.errCode

    env.recordResult('空格标题参数测试', hasError,
      hasError ? `正确返回错误：${result.errMsg}` : '未正确处理空格标题')

  } catch (error) {
    env.recordResult('空格标题参数测试', true, '正确抛出异常')
  }

  // 测试带有项目名称的任务创建
  try {
    const taskData = {
      title: '带项目的测试任务',
      content: '测试项目关联功能',
      projectName: '测试项目'
    }

    const result = await env.todoTool.createTask(taskData)
    const isSuccess = result && (result.errCode === null || result.errCode === undefined)

    env.recordResult('带项目名称创建任务', isSuccess,
      isSuccess ? '带项目任务创建成功' : `创建失败：${result?.errMsg || '未知错误'}`)

  } catch (error) {
    env.recordResult('带项目名称创建任务', false, `创建异常：${error.message}`)
  }

  // 测试带有标签的任务创建
  try {
    const taskData = {
      title: '带标签的测试任务',
      content: '测试标签关联功能',
      tagNames: ['测试标签', '重要']
    }

    const result = await env.todoTool.createTask(taskData)
    const isSuccess = result && (result.errCode === null || result.errCode === undefined)

    env.recordResult('带标签创建任务', isSuccess,
      isSuccess ? '带标签任务创建成功' : `创建失败：${result?.errMsg || '未知错误'}`)

  } catch (error) {
    env.recordResult('带标签创建任务', false, `创建异常：${error.message}`)
  }

  // 测试带有日期的任务创建
  try {
    const taskData = {
      title: '带日期的测试任务',
      content: '测试日期设置功能',
      startDate: '2024-12-01 09:00:00',
      dueDate: '2024-12-31 18:00:00',
      isAllDay: false
    }

    const result = await env.todoTool.createTask(taskData)
    const isSuccess = result && (result.errCode === null || result.errCode === undefined)

    env.recordResult('带日期创建任务', isSuccess,
      isSuccess ? '带日期任务创建成功' : `创建失败：${result?.errMsg || '未知错误'}`)

  } catch (error) {
    env.recordResult('带日期创建任务', false, `创建异常：${error.message}`)
  }

  // 测试全天任务创建
  try {
    const taskData = {
      title: '全天测试任务',
      content: '测试全天任务功能',
      dueDate: '2024-12-25',
      isAllDay: true
    }

    const result = await env.todoTool.createTask(taskData)
    const isSuccess = result && (result.errCode === null || result.errCode === undefined)

    env.recordResult('全天任务创建', isSuccess,
      isSuccess ? '全天任务创建成功' : `创建失败：${result?.errMsg || '未知错误'}`)

  } catch (error) {
    env.recordResult('全天任务创建', false, `创建异常：${error.message}`)
  }

  // 测试带提醒的任务创建
  try {
    const taskData = {
      title: '带提醒的测试任务',
      content: '测试提醒功能',
      dueDate: '2024-12-31 18:00:00',
      reminder: '-1H'
    }

    const result = await env.todoTool.createTask(taskData)
    const isSuccess = result && (result.errCode === null || result.errCode === undefined)

    env.recordResult('带提醒任务创建', isSuccess,
      isSuccess ? '带提醒任务创建成功' : `创建失败：${result?.errMsg || '未知错误'}`)

  } catch (error) {
    env.recordResult('带提醒任务创建', false, `创建异常：${error.message}`)
  }

  // 测试不同优先级的任务创建
  const priorities = [0, 1, 3, 5]
  for (const priority of priorities) {
    try {
      const taskData = {
        title: `优先级${priority}测试任务`,
        content: `测试优先级${priority}功能`,
        priority: priority
      }

      const result = await env.todoTool.createTask(taskData)
      const isSuccess = result && (result.errCode === null || result.errCode === undefined)

      env.recordResult(`优先级${priority}任务创建`, isSuccess,
        isSuccess ? `优先级${priority}任务创建成功` : `创建失败：${result?.errMsg || '未知错误'}`)

    } catch (error) {
      env.recordResult(`优先级${priority}任务创建`, false, `创建异常：${error.message}`)
    }
  }
}

/**
 * 错误处理测试
 */
async function testErrorHandling(env) {
  console.log('\n=== 错误处理测试 ===')

  try {
    // 测试无效方法调用
    const result = await env.todoTool.execute('invalidMethod', {})

    env.recordResult('无效方法处理', result.errCode !== undefined, result.errCode ? '正确返回错误' : '未正确处理错误')
  } catch (error) {
    env.recordResult('错误处理测试', true, '正确抛出异常')
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('开始 TodoTool 内置工具测试...\n')

  const env = new TestEnvironment()

  try {
    // 运行各项测试
    await testBasicFunctionality(env)
    await testCreateTask(env)
    await testPerformanceStats(env)
    await testErrorHandling(env)

    // 生成测试报告
    const report = env.generateReport()

    console.log('\n=== 测试报告 ===')
    console.log('测试摘要：', JSON.stringify(report.summary, null, 2))
    console.log('性能统计：', JSON.stringify(report.performanceStats, null, 2))

    // 输出详细结果
    console.log('\n=== 详细测试结果 ===')
    report.testResults.forEach((result) => {
      console.log(`${result.testName}: ${result.passed ? '✓' : '✗'} ${result.message}`)
    })

    return report
  } catch (error) {
    console.error('测试执行失败：', error)
    return null
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests().then(report => {
    if (report) {
      console.log('\n测试完成！')
      process.exit(report.summary.failedTests > 0 ? 1 : 0)
    } else {
      console.log('\n测试失败！')
      process.exit(1)
    }
  })
}

module.exports = {
  TestEnvironment,
  runTests,
  testBasicFunctionality,
  testCreateTask,
  testPerformanceStats,
  testErrorHandling,
}
