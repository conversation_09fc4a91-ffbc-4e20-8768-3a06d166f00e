# createTask 功能测试说明

## 概述

本文档说明了为 `createTask` 功能添加的测试用例，包括测试内容、运行方法和预期结果。

## 测试内容

### 1. 正常功能测试

- **正常创建任务**: 测试使用完整参数创建任务的基本功能
- **返回数据结构验证**: 验证创建成功后返回的数据结构是否正确

### 2. 参数验证测试

- **缺少必要参数测试**: 验证不传入任何参数时的错误处理
- **空标题参数测试**: 验证传入空字符串标题时的错误处理
- **空格标题参数测试**: 验证传入只包含空格的标题时的错误处理

### 3. 功能特性测试

- **带项目名称创建任务**: 测试关联项目的任务创建功能
- **带标签创建任务**: 测试关联标签的任务创建功能
- **带日期创建任务**: 测试设置开始日期和截止日期的功能
- **全天任务创建**: 测试创建全天任务的功能
- **带提醒任务创建**: 测试设置提醒的任务创建功能

### 4. 边界条件测试

- **不同优先级任务创建**: 测试创建不同优先级（0, 1, 3, 5）的任务

## 测试用例详情

### 正常创建任务测试

```javascript
const taskData = {
  title: '测试任务标题',
  content: '这是一个测试任务的详细内容',
  priority: 3,
  projectName: null,
  tagNames: [],
  startDate: null,
  dueDate: null,
  isAllDay: false,
  reminder: null
}
```

**验证点**:
- 返回结果格式正确（errCode 为 null 或 undefined）
- 返回数据包含必要字段（id 或 _id）
- 记录执行时间

### 参数验证测试

**测试场景**:
1. 空对象 `{}`
2. 空标题 `{ title: '' }`
3. 空格标题 `{ title: '   ' }`

**预期结果**: 所有场景都应该返回错误响应，errCode 不为空

### 功能特性测试

**带项目的任务**:
```javascript
{
  title: '带项目的测试任务',
  content: '测试项目关联功能',
  projectName: '测试项目'
}
```

**带标签的任务**:
```javascript
{
  title: '带标签的测试任务',
  content: '测试标签关联功能',
  tagNames: ['测试标签', '重要']
}
```

**带日期的任务**:
```javascript
{
  title: '带日期的测试任务',
  content: '测试日期设置功能',
  startDate: '2024-12-01 09:00:00',
  dueDate: '2024-12-31 18:00:00',
  isAllDay: false
}
```

## 运行方法

### 方法一：运行完整测试套件

```bash
cd uniCloud-aliyun/cloudfunctions/ai/test
node todo-tool.test.js
```

这将运行所有测试，包括 createTask 测试。

### 方法二：只运行 createTask 测试

```bash
cd uniCloud-aliyun/cloudfunctions/ai/test
node run-createTask-test.js
```

这将只运行 createTask 相关的测试用例。

### 方法三：在代码中调用

```javascript
const { testCreateTask, TestEnvironment } = require('./todo-tool.test')

async function runTest() {
  const env = new TestEnvironment()
  await testCreateTask(env)
  const report = env.generateReport()
  console.log(report)
}
```

## 预期输出

测试运行后会输出类似以下的结果：

```
=== createTask 功能测试 ===
[TEST] 正常创建任务: PASS - 任务创建成功，返回格式正确 (150ms)
[TEST] 返回数据结构验证: PASS - 返回数据包含必要字段 (0ms)
[TEST] 缺少必要参数测试: PASS - 正确返回错误：参数 title 不能为空 (0ms)
[TEST] 空标题参数测试: PASS - 正确返回错误：参数 title 不能为空 (0ms)
[TEST] 空格标题参数测试: PASS - 正确返回错误：参数 title 不能为空 (0ms)
...

=== createTask 专项统计 ===
总测试数: 12
通过数: 12
失败数: 0
成功率: 100.00%
```

## 注意事项

1. **网络依赖**: 测试需要网络连接来调用实际的 API 接口
2. **认证状态**: 确保 TodoTool 的认证配置正确
3. **测试数据**: 测试会创建实际的任务数据，可能需要清理
4. **独立运行**: 每个测试用例都设计为独立运行，不依赖其他测试的结果
5. **错误处理**: 测试包含了完整的错误处理，即使 API 调用失败也不会中断测试流程

## 扩展测试

如需添加更多测试用例，可以在 `testCreateTask` 函数中添加新的测试场景，遵循现有的测试模式：

```javascript
try {
  const taskData = { /* 测试数据 */ }
  const result = await env.todoTool.createTask(taskData)
  const isSuccess = result && (result.errCode === null || result.errCode === undefined)
  
  env.recordResult('测试名称', isSuccess, 
    isSuccess ? '成功消息' : `失败消息：${result?.errMsg || '未知错误'}`)
    
} catch (error) {
  env.recordResult('测试名称', false, `异常：${error.message}`)
}
```
