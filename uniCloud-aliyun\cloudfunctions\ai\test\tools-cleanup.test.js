/**
 * 工具调用引擎清理后的验证测试
 * 验证移除云函数调用兼容性代码后的功能正确性
 */

const { callRealTool } = require('../modules/tools')

/**
 * 测试内置工具调用功能
 */
async function testBuiltinToolCall() {
  console.log('=== 测试内置工具调用功能 ===')
  
  try {
    // 测试有效的内置工具调用
    console.log('\n1. 测试有效的内置工具调用...')
    
    // 注意：这里会因为没有真实的认证而失败，但我们主要测试调用机制
    try {
      const result = await callRealTool('getTasks', { mode: 'all' })
      console.log('✓ 内置工具调用机制正常')
    } catch (error) {
      if (error.message.includes('认证') || error.message.includes('token')) {
        console.log('✓ 内置工具调用机制正常（预期的认证错误）')
      } else {
        throw error
      }
    }
    
  } catch (error) {
    console.error('✗ 内置工具调用测试失败:', error.message)
    return false
  }
  
  return true
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('\n=== 测试错误处理 ===')
  
  const testCases = [
    {
      name: '未找到工具',
      toolName: 'nonExistentTool',
      parameters: {},
      expectedError: '未找到工具'
    },
    {
      name: '非内置工具类型',
      toolName: 'mockCloudFunctionTool',
      parameters: {},
      expectedError: '不是内置工具类型'
    }
  ]
  
  let allPassed = true
  
  for (const testCase of testCases) {
    try {
      console.log(`\n测试: ${testCase.name}`)
      
      await callRealTool(testCase.toolName, testCase.parameters)
      console.log(`✗ 应该抛出错误但没有抛出`)
      allPassed = false
      
    } catch (error) {
      if (error.message.includes(testCase.expectedError)) {
        console.log(`✓ 正确处理错误: ${error.message}`)
      } else {
        console.log(`✗ 错误消息不匹配，期望包含: ${testCase.expectedError}，实际: ${error.message}`)
        allPassed = false
      }
    }
  }
  
  return allPassed
}

/**
 * 测试工具实例缓存
 */
async function testToolInstanceCaching() {
  console.log('\n=== 测试工具实例缓存 ===')
  
  try {
    // 多次调用同一个工具，验证实例是否被缓存
    console.log('\n测试工具实例缓存机制...')
    
    const startTime = Date.now()
    
    // 第一次调用 - 会创建实例
    try {
      await callRealTool('getTasks', { mode: 'all' })
    } catch (error) {
      // 忽略认证错误
      if (!error.message.includes('认证') && !error.message.includes('token')) {
        throw error
      }
    }
    
    // 第二次调用 - 应该使用缓存的实例
    try {
      await callRealTool('getTasks', { mode: 'recent_7_days' })
    } catch (error) {
      // 忽略认证错误
      if (!error.message.includes('认证') && !error.message.includes('token')) {
        throw error
      }
    }
    
    const duration = Date.now() - startTime
    console.log(`✓ 工具实例缓存机制正常 (总耗时: ${duration}ms)`)
    
    return true
    
  } catch (error) {
    console.error('✗ 工具实例缓存测试失败:', error.message)
    return false
  }
}

/**
 * 测试参数传递
 */
async function testParameterPassing() {
  console.log('\n=== 测试参数传递 ===')
  
  try {
    const testParameters = {
      mode: 'all',
      keyword: 'test',
      priority: 3,
      completed: false
    }
    
    console.log('\n测试参数传递...')
    
    try {
      await callRealTool('getTasks', testParameters)
      console.log('✓ 参数传递机制正常')
    } catch (error) {
      if (error.message.includes('认证') || error.message.includes('token')) {
        console.log('✓ 参数传递机制正常（预期的认证错误）')
      } else {
        throw error
      }
    }
    
    return true
    
  } catch (error) {
    console.error('✗ 参数传递测试失败:', error.message)
    return false
  }
}

/**
 * 主测试函数
 */
async function runCleanupTests() {
  console.log('开始工具调用引擎清理验证测试...\n')
  
  const testResults = []
  
  // 运行各项测试
  testResults.push(await testBuiltinToolCall())
  testResults.push(await testErrorHandling())
  testResults.push(await testToolInstanceCaching())
  testResults.push(await testParameterPassing())
  
  // 统计结果
  const totalTests = testResults.length
  const passedTests = testResults.filter(result => result).length
  const failedTests = totalTests - passedTests
  
  console.log('\n=== 测试结果摘要 ===')
  console.log(`总测试数: ${totalTests}`)
  console.log(`通过测试: ${passedTests}`)
  console.log(`失败测试: ${failedTests}`)
  console.log(`成功率: ${(passedTests / totalTests * 100).toFixed(1)}%`)
  
  if (failedTests === 0) {
    console.log('\n🎉 所有测试通过！工具调用引擎清理成功。')
    console.log('✅ 云函数调用兼容性代码已成功移除')
    console.log('✅ 内置工具调用功能正常')
    console.log('✅ 错误处理机制完善')
    console.log('✅ 工具实例缓存有效')
  } else {
    console.log('\n⚠️ 部分测试失败，需要检查相关功能。')
  }
  
  return {
    success: failedTests === 0,
    totalTests,
    passedTests,
    failedTests
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runCleanupTests().then(result => {
    process.exit(result.success ? 0 : 1)
  }).catch(error => {
    console.error('测试执行失败:', error)
    process.exit(1)
  })
}

module.exports = {
  runCleanupTests,
  testBuiltinToolCall,
  testErrorHandling,
  testToolInstanceCaching,
  testParameterPassing
}
